import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import dotenv from 'dotenv';
import * as schema from '@shared/schema';

dotenv.config();

const pool = new Pool({
  host: '**************',
  port: 3003,
  user: 'rasaadmin',
  password: 'gm8122x&1}r{',
  database: 'rasa1db',
  ssl: false,
});

pool.connect()
  .then(() => console.log('Database connection successful'))
  .catch((err) => console.error('Database connection failed:', err));

export const db = drizzle(pool, { schema });
